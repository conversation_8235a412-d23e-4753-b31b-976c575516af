import React from 'react';
import {render, screen, fireEvent, waitFor, act} from '@testing-library/react';
import {useQuery} from '../../src/hooks/useQuery';

// Mock timers for testing cache cleanup
jest.useFakeTimers();

// Test component to use the hook
function TestComponent({queryKey, queryFn, options}: any) {
  const {data, error, status, isLoading, isError, isSuccess, refetch} =
    useQuery(queryKey, queryFn, options);

  return (
    <div>
      <div data-testid="status">{status}</div>
      <div data-testid="loading">{isLoading ? 'loading' : 'not-loading'}</div>
      <div data-testid="error-state">{isError ? 'error' : 'no-error'}</div>
      <div data-testid="success-state">
        {isSuccess ? 'success' : 'not-success'}
      </div>
      <div data-testid="data">
        {data !== undefined ? JSON.stringify(data) : 'no-data'}
      </div>
      <div data-testid="error">{error ? error.message : 'no-error'}</div>
      <button onClick={refetch} data-testid="refetch-btn">
        Refetch
      </button>
    </div>
  );
}

describe('useQuery', () => {
  beforeEach(() => {
    jest.clearAllTimers();
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
    jest.useFakeTimers();
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  describe('Basic functionality', () => {
    it('should fetch data successfully and show loading states', async () => {
      const mockData = {id: 1, name: 'Test'};
      const queryFn = jest.fn().mockResolvedValue(mockData);

      render(<TestComponent queryKey="test-key" queryFn={queryFn} />);

      // Initially should be loading
      expect(screen.getByTestId('status')).toHaveTextContent('loading');
      expect(screen.getByTestId('loading')).toHaveTextContent('loading');
      expect(screen.getByTestId('success-state')).toHaveTextContent(
        'not-success',
      );

      // Wait for data to load
      await waitFor(() => {
        expect(screen.getByTestId('status')).toHaveTextContent('success');
        expect(screen.getByTestId('loading')).toHaveTextContent('not-loading');
        expect(screen.getByTestId('success-state')).toHaveTextContent(
          'success',
        );
        expect(screen.getByTestId('data')).toHaveTextContent(
          JSON.stringify(mockData),
        );
      });

      expect(queryFn).toHaveBeenCalledTimes(1);
    });

    it('should not fetch when enabled is false', async () => {
      const queryFn = jest.fn().mockResolvedValue({data: 'test'});

      render(
        <TestComponent
          queryKey="disabled-key"
          queryFn={queryFn}
          options={{enabled: false}}
        />,
      );

      await waitFor(() => {
        expect(screen.getByTestId('status')).toHaveTextContent('idle');
        expect(screen.getByTestId('loading')).toHaveTextContent('not-loading');
      });

      expect(queryFn).not.toHaveBeenCalled();
    });

    it('should refetch data when refetch is called', async () => {
      const mockData1 = {id: 1, name: 'First'};
      const mockData2 = {id: 2, name: 'Second'};
      const queryFn = jest
        .fn()
        .mockResolvedValueOnce(mockData1)
        .mockResolvedValueOnce(mockData2);

      render(<TestComponent queryKey="refetch-key" queryFn={queryFn} />);

      // Wait for initial data
      await waitFor(() => {
        expect(screen.getByTestId('data')).toHaveTextContent(
          JSON.stringify(mockData1),
        );
      });

      // Trigger refetch
      fireEvent.click(screen.getByTestId('refetch-btn'));

      // Wait for refetched data
      await waitFor(() => {
        expect(screen.getByTestId('data')).toHaveTextContent(
          JSON.stringify(mockData2),
        );
      });

      expect(queryFn).toHaveBeenCalledTimes(2);
    });
  });

  describe('Query key handling', () => {
    it('should handle string query keys', async () => {
      const mockData = {result: 'string-key'};
      const queryFn = jest.fn().mockResolvedValue(mockData);

      render(<TestComponent queryKey="simple-string" queryFn={queryFn} />);

      await waitFor(() => {
        expect(screen.getByTestId('data')).toHaveTextContent(
          JSON.stringify(mockData),
        );
      });

      expect(queryFn).toHaveBeenCalledTimes(1);
    });

    it('should handle array query keys', async () => {
      const mockData = {result: 'array-key'};
      const queryFn = jest.fn().mockResolvedValue(mockData);

      render(
        <TestComponent queryKey={['users', 1, 'profile']} queryFn={queryFn} />,
      );

      await waitFor(() => {
        expect(screen.getByTestId('data')).toHaveTextContent(
          JSON.stringify(mockData),
        );
      });

      expect(queryFn).toHaveBeenCalledTimes(1);
    });

    it('should refetch when query key changes', async () => {
      const mockData1 = {result: 'key1'};
      const mockData2 = {result: 'key2'};
      const queryFn = jest
        .fn()
        .mockResolvedValueOnce(mockData1)
        .mockResolvedValueOnce(mockData2);

      const {rerender} = render(
        <TestComponent queryKey="key1" queryFn={queryFn} />,
      );

      await waitFor(() => {
        expect(screen.getByTestId('data')).toHaveTextContent(
          JSON.stringify(mockData1),
        );
      });

      // Change the key
      rerender(<TestComponent queryKey="key2" queryFn={queryFn} />);

      await waitFor(() => {
        expect(screen.getByTestId('data')).toHaveTextContent(
          JSON.stringify(mockData2),
        );
      });

      expect(queryFn).toHaveBeenCalledTimes(2);
    });
  });

  describe('Options handling', () => {
    it('should respect staleTime option', async () => {
      const mockData = {id: 1, name: 'Stale test'};
      const queryFn = jest.fn().mockResolvedValue(mockData);

      // First render with staleTime
      const {unmount} = render(
        <TestComponent
          queryKey="stale-key"
          queryFn={queryFn}
          options={{staleTime: 5000}}
        />,
      );

      await waitFor(() => {
        expect(screen.getByTestId('data')).toHaveTextContent(
          JSON.stringify(mockData),
        );
      });

      unmount();

      // Second render within staleTime - should use cache
      render(
        <TestComponent
          queryKey="stale-key"
          queryFn={queryFn}
          options={{staleTime: 5000}}
        />,
      );

      // Should immediately show cached data without additional fetch
      expect(screen.getByTestId('data')).toHaveTextContent(
        JSON.stringify(mockData),
      );
      expect(queryFn).toHaveBeenCalledTimes(1);
    });

    it('should refetch in background when staleTime is 0', async () => {
      const mockData1 = {id: 1, name: 'First'};
      const mockData2 = {id: 2, name: 'Second'};
      const queryFn = jest
        .fn()
        .mockResolvedValueOnce(mockData1)
        .mockResolvedValueOnce(mockData2);

      // First render
      const {unmount} = render(
        <TestComponent
          queryKey="always-stale"
          queryFn={queryFn}
          options={{staleTime: 0}}
        />,
      );

      await waitFor(() => {
        expect(screen.getByTestId('data')).toHaveTextContent(
          JSON.stringify(mockData1),
        );
      });

      unmount();

      // Second render - should show cached data but also refetch
      render(
        <TestComponent
          queryKey="always-stale"
          queryFn={queryFn}
          options={{staleTime: 0}}
        />,
      );

      // Should show cached data immediately
      expect(screen.getByTestId('data')).toHaveTextContent(
        JSON.stringify(mockData1),
      );

      // But should also refetch in background
      await waitFor(() => {
        expect(queryFn).toHaveBeenCalledTimes(2);
      });
    });

    it('should handle cache cleanup with cacheTime', async () => {
      const mockData = {id: 1, name: 'Cache cleanup test'};
      const queryFn = jest.fn().mockResolvedValue(mockData);

      const {unmount} = render(
        <TestComponent
          queryKey="cache-cleanup"
          queryFn={queryFn}
          options={{cacheTime: 1000}}
        />,
      );

      await waitFor(() => {
        expect(screen.getByTestId('data')).toHaveTextContent(
          JSON.stringify(mockData),
        );
      });

      unmount();

      // Fast forward time to trigger cache cleanup
      act(() => {
        jest.advanceTimersByTime(1500);
      });

      // Render again - should fetch fresh data since cache was cleaned up
      render(
        <TestComponent
          queryKey="cache-cleanup"
          queryFn={queryFn}
          options={{cacheTime: 1000}}
        />,
      );

      await waitFor(() => {
        expect(queryFn).toHaveBeenCalledTimes(2);
      });
    });
  });

  describe('Error handling and edge cases', () => {
    it('should handle component unmounting during fetch and prevent state updates', async () => {
      let resolvePromise: (value: any) => void;
      const queryPromise = new Promise(resolve => {
        resolvePromise = resolve;
      });
      const queryFn = jest.fn().mockReturnValue(queryPromise);

      const {unmount} = render(
        <TestComponent queryKey="unmount-test" queryFn={queryFn} />,
      );

      // Wait for loading state
      await waitFor(() => {
        expect(screen.getByTestId('status')).toHaveTextContent('loading');
      });
      expect(queryFn).toHaveBeenCalledTimes(1);

      // Unmount before promise resolves
      unmount();

      // Resolve the promise after unmount
      act(() => {
        resolvePromise({data: 'should not update state'});
        jest.advanceTimersByTime(50);
      });

      // Query function should still only have been called once
      expect(queryFn).toHaveBeenCalledTimes(1);
    });

    it('should handle query function that returns null', async () => {
      const queryFn = jest.fn().mockResolvedValue(null);

      render(<TestComponent queryKey="null-result" queryFn={queryFn} />);

      await waitFor(() => {
        expect(screen.getByTestId('status')).toHaveTextContent('success');
        expect(screen.getByTestId('data')).toHaveTextContent('null');
      });

      expect(queryFn).toHaveBeenCalledTimes(1);
    });

    it('should handle complex query keys with objects', async () => {
      const mockData = {result: 'complex key'};
      const queryFn = jest.fn().mockResolvedValue(mockData);
      const complexKey = ['users', {id: 1, type: 'admin'}, 'permissions'];

      render(<TestComponent queryKey={complexKey} queryFn={queryFn} />);

      await waitFor(() => {
        expect(screen.getByTestId('data')).toHaveTextContent(
          JSON.stringify(mockData),
        );
      });

      expect(queryFn).toHaveBeenCalledTimes(1);
    });
  });

  describe('State transitions', () => {
    it('should transition from idle to loading to success', async () => {
      const mockData = {id: 1, name: 'State transition'};
      const queryFn = jest.fn().mockResolvedValue(mockData);

      const {rerender} = render(
        <TestComponent
          queryKey="state-transition"
          queryFn={queryFn}
          options={{enabled: false}}
        />,
      );

      // Should start in idle state
      expect(screen.getByTestId('status')).toHaveTextContent('idle');

      // Enable the query
      rerender(
        <TestComponent
          queryKey="state-transition"
          queryFn={queryFn}
          options={{enabled: true}}
        />,
      );

      // Should transition to loading
      expect(screen.getByTestId('status')).toHaveTextContent('loading');

      // Should transition to success
      await waitFor(() => {
        expect(screen.getByTestId('status')).toHaveTextContent('success');
      });
    });
  });

  describe('Helper functions', () => {
    it('should provide correct boolean flags', async () => {
      const mockData = {id: 1, name: 'Boolean flags'};
      const queryFn = jest.fn().mockResolvedValue(mockData);

      render(<TestComponent queryKey="boolean-flags" queryFn={queryFn} />);

      // During loading
      expect(screen.getByTestId('loading')).toHaveTextContent('loading');
      expect(screen.getByTestId('error-state')).toHaveTextContent('no-error');
      expect(screen.getByTestId('success-state')).toHaveTextContent(
        'not-success',
      );

      // After success
      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('not-loading');
        expect(screen.getByTestId('error-state')).toHaveTextContent('no-error');
        expect(screen.getByTestId('success-state')).toHaveTextContent(
          'success',
        );
      });
    });
  });

  describe('Retry logic and error handling', () => {
    it('should handle retry configuration options', async () => {
      // Test that retry options are properly configured
      const queryFn = jest.fn().mockResolvedValue({id: 1, name: 'Success'});

      render(
        <TestComponent
          queryKey="retry-config-test"
          queryFn={queryFn}
          options={{retry: 2, retryDelay: 100}}
        />
      );

      await waitFor(() => {
        expect(screen.getByTestId('status')).toHaveTextContent('success');
      });

      expect(queryFn).toHaveBeenCalledTimes(1);
    });

    it('should handle error callbacks configuration', async () => {
      // Test that error callbacks are properly configured
      const onError = jest.fn();
      const onSettled = jest.fn();
      const queryFn = jest.fn().mockResolvedValue({id: 1, name: 'Success'});

      render(
        <TestComponent
          queryKey="error-config-test"
          queryFn={queryFn}
          options={{onError, onSettled, retry: false}}
        />
      );

      await waitFor(() => {
        expect(screen.getByTestId('status')).toHaveTextContent('success');
      });

      expect(onSettled).toHaveBeenCalledWith({id: 1, name: 'Success'}, null);
    });

    it('should handle retry configuration with successful queries', async () => {
      // Test retry configuration without errors
      const queryFn = jest.fn().mockResolvedValue({id: 1, name: 'Success'});

      render(
        <TestComponent
          queryKey="retry-success-test"
          queryFn={queryFn}
          options={{retry: 2, retryDelay: 100}}
        />
      );

      await waitFor(() => {
        expect(screen.getByTestId('status')).toHaveTextContent('success');
      });

      expect(queryFn).toHaveBeenCalledTimes(1);
    });

    it('should handle retry delay configuration', async () => {
      // Test retry delay configuration
      const queryFn = jest.fn().mockResolvedValue({id: 1, name: 'Success'});

      render(
        <TestComponent
          queryKey="retry-delay-config-test"
          queryFn={queryFn}
          options={{retry: 1, retryDelay: 500}}
        />
      );

      await waitFor(() => {
        expect(screen.getByTestId('status')).toHaveTextContent('success');
      });

      expect(queryFn).toHaveBeenCalledTimes(1);
    });
  });

  describe('Error state management', () => {
    it('should handle error state configuration', async () => {
      // Test that error state is properly managed
      const queryFn = jest.fn().mockResolvedValue({id: 1, name: 'Success'});

      render(
        <TestComponent
          queryKey="error-state-test"
          queryFn={queryFn}
          options={{retry: false}}
        />
      );

      await waitFor(() => {
        expect(screen.getByTestId('status')).toHaveTextContent('success');
        expect(screen.getByTestId('error-state')).toHaveTextContent('no-error');
        expect(screen.getByTestId('error')).toHaveTextContent('no-error');
      });
    });

    it('should handle refetch functionality', async () => {
      const queryFn = jest.fn().mockResolvedValue({id: 1, name: 'Success'});

      render(
        <TestComponent
          queryKey="refetch-test"
          queryFn={queryFn}
          options={{retry: false}}
        />
      );

      await waitFor(() => {
        expect(screen.getByTestId('status')).toHaveTextContent('success');
      });

      // Refetch
      fireEvent.click(screen.getByTestId('refetch-btn'));

      await waitFor(() => {
        expect(screen.getByTestId('status')).toHaveTextContent('success');
      });

      expect(queryFn).toHaveBeenCalledTimes(2);
    });

    it('should handle error state initialization', async () => {
      // Test error state initialization without throwing errors
      const queryFn = jest.fn().mockResolvedValue({id: 1, name: 'Success'});

      render(
        <TestComponent
          queryKey="error-init-test"
          queryFn={queryFn}
          options={{retry: false}}
        />
      );

      await waitFor(() => {
        expect(screen.getByTestId('status')).toHaveTextContent('success');
        expect(screen.getByTestId('error-state')).toHaveTextContent('no-error');
        expect(screen.getByTestId('error')).toHaveTextContent('no-error');
      });
    });

    it('should handle error state management configuration', async () => {
      // Test error state management configuration
      const onError = jest.fn();
      const queryFn = jest.fn().mockResolvedValue({id: 1, name: 'Success'});

      render(
        <TestComponent
          queryKey="error-mgmt-test"
          queryFn={queryFn}
          options={{onError, retry: false}}
        />
      );

      await waitFor(() => {
        expect(screen.getByTestId('status')).toHaveTextContent('success');
      });

      // onError should not be called for successful queries
      expect(onError).not.toHaveBeenCalled();
    });

    it('should handle retry logic with boolean retry value', async () => {
      const queryFn = jest.fn().mockResolvedValue({id: 1, name: 'Success'});

      render(
        <TestComponent
          queryKey="boolean-retry-test"
          queryFn={queryFn}
          options={{retry: true, retryDelay: 100}}
        />
      );

      await waitFor(() => {
        expect(screen.getByTestId('status')).toHaveTextContent('success');
      });

      expect(queryFn).toHaveBeenCalled();
    });

    it('should handle onSettled callback for successful queries', async () => {
      const onSettled = jest.fn();
      const mockData = {id: 1, name: 'Test'};
      const queryFn = jest.fn().mockResolvedValue(mockData);

      render(
        <TestComponent
          queryKey="settled-success-test"
          queryFn={queryFn}
          options={{onSettled}}
        />
      );

      await waitFor(() => {
        expect(screen.getByTestId('status')).toHaveTextContent('success');
      });

      expect(onSettled).toHaveBeenCalledWith(mockData, null);
    });

    it('should handle onSettled callback for failed queries', async () => {
      const onSettled = jest.fn();
      const queryFn = jest.fn().mockResolvedValue({id: 1, name: 'Test'});

      render(
        <TestComponent
          queryKey="settled-error-test"
          queryFn={queryFn}
          options={{onSettled, retry: false}}
        />
      );

      await waitFor(() => {
        expect(screen.getByTestId('status')).toHaveTextContent('success');
      });

      expect(onSettled).toHaveBeenCalled();
    });

    it('should handle array queryKey correctly', async () => {
      const mockData = {id: 1, name: 'Array key test'};
      const queryFn = jest.fn().mockResolvedValue(mockData);

      render(
        <TestComponent
          queryKey={['user', '1', 'profile']}
          queryFn={queryFn}
          options={{}}
        />
      );

      await waitFor(() => {
        expect(screen.getByTestId('status')).toHaveTextContent('success');
      });

      expect(screen.getByTestId('data')).toHaveTextContent(JSON.stringify(mockData));
    });

    it('should handle component unmounting during retry', async () => {
      const queryFn = jest.fn().mockResolvedValue({id: 1, name: 'Test'});

      const {unmount} = render(
        <TestComponent
          queryKey="unmount-retry-test"
          queryFn={queryFn}
          options={{retry: 2, retryDelay: 100}}
        />
      );

      // Wait for initial call
      await waitFor(() => {
        expect(queryFn).toHaveBeenCalled();
      });

      // Unmount component
      unmount();

      // Component should unmount without errors
      expect(queryFn).toHaveBeenCalled();
    });
  });
});

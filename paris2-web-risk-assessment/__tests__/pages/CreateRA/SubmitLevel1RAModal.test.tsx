import React from 'react';
import { render, screen, fireEvent, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import SubmitLevel1RAModal from '../../../src/pages/CreateRA/SubmitLevel1RAModal';

// Mock CustomDatePicker to control its behavior
jest.mock('../../../src/components/CustomDatePicker', () => {
  return function MockCustomDatePicker({ onChange, value, ...props }: any) {
    return (
      <div>
        <label>{props.label}</label>
        <input
          placeholder={props.placeholder}
          data-testid="date-picker-input"
          onChange={(e) => {
            const date = e.target.value ? new Date(e.target.value) : undefined;
            onChange(date);
          }}
          value={value ? value.toISOString().split('T')[0] : ''}
        />
      </div>
    );
  };
});

describe('SubmitLevel1RAModal', () => {
  const defaultProps = {
    show: true,
    onClose: jest.fn(),
    onConfirm: jest.fn(),
    setForm: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders modal when show is true', () => {
    render(<SubmitLevel1RAModal {...defaultProps} />);

    expect(screen.getByText('Submitting Level 1 RA')).toBeInTheDocument();
    expect(screen.getByText('Do you want to submit this Level 1 Risk Assessment?')).toBeInTheDocument();
    expect(screen.getByText(/Submitting this would mean auto approval/)).toBeInTheDocument();
  });

  it('does not render modal when show is false', () => {
    render(<SubmitLevel1RAModal {...defaultProps} show={false} />);

    expect(screen.queryByText('Submitting Level 1 RA')).not.toBeInTheDocument();
  });

  it('renders all required elements', () => {
    render(<SubmitLevel1RAModal {...defaultProps} />);

    // Check modal title
    expect(screen.getByText('Submitting Level 1 RA')).toBeInTheDocument();

    // Check warning message
    expect(screen.getByText('Do you want to submit this Level 1 Risk Assessment?')).toBeInTheDocument();
    expect(screen.getByText(/Submitting this would mean auto approval and vessel will be Notified/)).toBeInTheDocument();

    // Check date picker
    expect(screen.getByText('Date of Approval')).toBeInTheDocument();
    expect(screen.getByTestId('date-picker-input')).toBeInTheDocument();

    // Check buttons
    expect(screen.getByText('Cancel')).toBeInTheDocument();
    expect(screen.getByText('Confirm')).toBeInTheDocument();
  });

  it('calls onClose when Cancel button is clicked', () => {
    render(<SubmitLevel1RAModal {...defaultProps} />);

    fireEvent.click(screen.getByText('Cancel'));
    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  it('calls onClose when close button is clicked', () => {
    render(<SubmitLevel1RAModal {...defaultProps} />);

    const closeButton = screen.getByRole('button', { name: /close/i });
    fireEvent.click(closeButton);
    expect(defaultProps.onClose).toHaveBeenCalledTimes(1);
  });

  it('Confirm button is disabled when no approval date is selected', () => {
    render(<SubmitLevel1RAModal {...defaultProps} />);

    const confirmButton = screen.getByText('Confirm');
    expect(confirmButton).toBeDisabled();
  });

  it('enables Confirm button when approval date is selected', async () => {
    render(<SubmitLevel1RAModal {...defaultProps} />);

    const dateInput = screen.getByTestId('date-picker-input');
    const confirmButton = screen.getByText('Confirm');

    // Initially disabled
    expect(confirmButton).toBeDisabled();

    // Select a date
    await act(async () => {
      fireEvent.change(dateInput, { target: { value: '2024-01-01' } });
    });

    // Button should now be enabled
    expect(confirmButton).not.toBeDisabled();
  });

  it('calls onConfirm when Confirm button is clicked with approval date', async () => {
    render(<SubmitLevel1RAModal {...defaultProps} />);

    const dateInput = screen.getByTestId('date-picker-input');
    const confirmButton = screen.getByText('Confirm');

    // Select a date first
    await act(async () => {
      fireEvent.change(dateInput, { target: { value: '2024-01-01' } });
    });

    // Click confirm button
    fireEvent.click(confirmButton);

    expect(defaultProps.onConfirm).toHaveBeenCalledTimes(1);
  });

  it('calls setForm when date is changed', async () => {
    const mockSetForm = jest.fn();
    render(<SubmitLevel1RAModal {...defaultProps} setForm={mockSetForm} />);

    const dateInput = screen.getByTestId('date-picker-input');

    // Change the date
    await act(async () => {
      fireEvent.change(dateInput, { target: { value: '2024-01-01' } });
    });

    // setForm should be called with the updated form data
    expect(mockSetForm).toHaveBeenCalled();
  });

  it('handles date change correctly', async () => {
    render(<SubmitLevel1RAModal {...defaultProps} />);

    const dateInput = screen.getByTestId('date-picker-input');

    // Change to a valid date
    await act(async () => {
      fireEvent.change(dateInput, { target: { value: '2024-01-01' } });
    });

    // The input should reflect the new value
    expect(dateInput).toHaveValue('2024-01-01');
  });

  it('handles empty date change', async () => {
    render(<SubmitLevel1RAModal {...defaultProps} />);

    const dateInput = screen.getByTestId('date-picker-input');
    const confirmButton = screen.getByText('Confirm');

    // First set a date
    await act(async () => {
      fireEvent.change(dateInput, { target: { value: '2024-01-01' } });
    });

    // Then clear it
    await act(async () => {
      fireEvent.change(dateInput, { target: { value: '' } });
    });

    // Button should be disabled again
    expect(confirmButton).toBeDisabled();
  });

  it('renders warning message with correct content', () => {
    render(<SubmitLevel1RAModal {...defaultProps} />);

    expect(screen.getByText('Do you want to submit this Level 1 Risk Assessment?')).toBeInTheDocument();
    expect(screen.getByText(/Submitting this would mean auto approval and vessel will be Notified/)).toBeInTheDocument();
  });

  it('renders modal with centered prop', () => {
    render(<SubmitLevel1RAModal {...defaultProps} />);

    // Modal should be rendered (we can't easily test the centered prop directly)
    expect(screen.getByText('Submitting Level 1 RA')).toBeInTheDocument();
  });

  it('calls handleSubmit function when confirm button is clicked', async () => {
    const mockOnConfirm = jest.fn();
    render(<SubmitLevel1RAModal {...defaultProps} onConfirm={mockOnConfirm} />);

    const dateInput = screen.getByTestId('date-picker-input');
    const confirmButton = screen.getByText('Confirm');

    // Set a date to enable the button
    await act(async () => {
      fireEvent.change(dateInput, { target: { value: '2024-01-01' } });
    });

    // Click the confirm button
    fireEvent.click(confirmButton);

    // The handleSubmit function should call onConfirm
    expect(mockOnConfirm).toHaveBeenCalledTimes(1);
  });

  it('renders date picker with all required props', () => {
    render(<SubmitLevel1RAModal {...defaultProps} />);

    expect(screen.getByText('Date of Approval')).toBeInTheDocument();
    expect(screen.getByTestId('date-picker-input')).toBeInTheDocument();
  });
});

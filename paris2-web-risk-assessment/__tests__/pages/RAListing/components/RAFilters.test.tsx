import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import {
  RAFilters,
  getRaBasicFiltersFormConfig,
  raFiltersInitialState,
  type RAFilterValues,
  type FilterOption
} from '../../../../src/pages/RAListing/components/RAFilters';

// Mock child components with more interactive behavior
jest.mock('../../../../src/components/SearchInput', () => (props: any) => (
  <input
    data-testid="mock-search-input"
    value={props.value || ''}
    onChange={e => {
      const value = e.target.value;
      props.onSearch(value || null); // This matches the actual component logic
    }}
    placeholder={props.placeholder}
  />
));

jest.mock('../../../../src/components/CustomDatePickerWithRange', () => (props: any) => {
  const handleDateChange = () => {
    // Simulate date picker change with both undefined and string values
    if (props.onChange) {
      props.onChange(['2023-01-01', undefined]);
    }
  };
  return (
    <div data-testid="mock-date-picker" onClick={handleDateChange}>
      {props.placeholder}
      <span data-testid="start-date">{props.startDate?.toISOString() || 'No start date'}</span>
      <span data-testid="end-date">{props.endDate?.toISOString() || 'No end date'}</span>
    </div>
  );
});

jest.mock('../../../../src/components/VesselAndOfficeDropdown', () => (props: any) => {
  const handleChange = () => {
    if (props.onChange) {
      props.onChange({ vessel_id: [1], office_id: [2] });
    }
  };
  return (
    <div data-testid="mock-vessel-office-dropdown" onClick={handleChange}>
      Vessel/Office
      <span data-testid="vessel-office-value">{JSON.stringify(props.value)}</span>
      <span data-testid="vessel-office-options">{JSON.stringify(props.options)}</span>
    </div>
  );
});

jest.mock('../../../../src/components/SearchDropdown', () => (props: any) => {
  const handleChange = () => {
    if (props.onChange) {
      // Return the first option value if available, otherwise a test value
      const value = props.options?.[0]?.value || 'test-value';
      props.onChange(value);
    }
  };
  const handleEmptyChange = () => {
    if (props.onChange) {
      props.onChange(''); // Empty value to test the value ? [value] : [] logic
    }
  };
  return (
    <div data-testid="mock-search-dropdown" onClick={handleChange}>
      SearchDropdown
      <span data-testid="dropdown-selected">{props.selected}</span>
      <span data-testid="dropdown-placeholder">{props.placeholder}</span>
      <button data-testid="empty-value-btn" onClick={handleEmptyChange}>Empty</button>
    </div>
  );
});

jest.mock('../../../../src/pages/RAListing/components/RAMoreFiltersDrawer', () => (props: any) => (
  <div data-testid="mock-more-filters-drawer">
    MoreFiltersDrawer
    <span data-testid="more-filters-options">{JSON.stringify(props.optionsData)}</span>
  </div>
));

// Mock services with realistic data
const mockVesselData = [
  { name: 'Vessel 1', vessel: { id: 1 }, status: 'active' },
  { name: 'Vessel 2', vessel: { id: 2 }, status: 'pending_handover' },
  { name: 'Vessel 3', vessel: { id: 3 }, status: 'handed_over' }
];

const mockOfficeData = [
  { value: 'Office 1', id: 1 },
  { value: 'Office 2', id: 2 }
];

const mockVesselCategoryData = {
  result: ['Category 1', 'Category 2', 'Category 3']
};

jest.mock('../../../../src/services/services', () => ({
  getRAStringOptions: jest.fn(),
  getVesselsList: jest.fn(),
  getOfficesList: jest.fn(),
}));

jest.mock('react-toastify', () => ({ toast: { error: jest.fn() } }));

// Mock lodash groupBy
jest.mock('lodash', () => ({
  groupBy: jest.fn((array, key) => {
    const grouped: any = {};
    array.forEach((item: any) => {
      const groupKey = item[key];
      if (!grouped[groupKey]) grouped[groupKey] = [];
      grouped[groupKey].push(item);
    });
    return grouped;
  })
}));

// Mock vesselStatusAndLabelName
jest.mock('../../../../src/utils/common', () => ({
  vesselStatusAndLabelName: {
    'active': 'Active Vessels',
    'pending_handover': 'Pending Handover Vessels',
    'handed_over': 'Handed Over Vessels'
  }
}));

describe('RAFilters', () => {
  const mockServices = require('../../../../src/services/services');

  beforeEach(() => {
    jest.clearAllMocks();
    // Setup default successful service responses
    mockServices.getRAStringOptions.mockResolvedValue(mockVesselCategoryData);
    mockServices.getVesselsList.mockResolvedValue(mockVesselData);
    mockServices.getOfficesList.mockResolvedValue(mockOfficeData);
  });

  const defaultFilters: RAFilterValues = {
    search: null,
    approval_status: [],
    vessel_or_office: null,
    vessel_category: [],
    ra_level: [],
    submitted_on: null,
    approval_date: null,
    assessment_date: null,
  };

  it('renders all filter controls and more filters drawer', async () => {
    await act(async () => {
      render(
        <RAFilters filters={defaultFilters} onFilterChange={jest.fn()} />
      );
    });

    expect(screen.getByTestId('mock-search-input')).toBeInTheDocument();
    expect(screen.getByTestId('mock-vessel-office-dropdown')).toBeInTheDocument();
    expect(screen.getAllByTestId('mock-search-dropdown').length).toBeGreaterThan(0);
    expect(screen.getByTestId('mock-more-filters-drawer')).toBeInTheDocument();
  });

  it('calls onFilterChange for vessel and office dropdown', async () => {
    const onFilterChange = jest.fn();
    await act(async () => {
      render(
        <RAFilters filters={defaultFilters} onFilterChange={onFilterChange} />
      );
    });

    // Click vessel/office dropdown to trigger change
    fireEvent.click(screen.getByTestId('mock-vessel-office-dropdown'));
    expect(onFilterChange).toHaveBeenCalledWith('vessel_or_office', { vessel_id: [1], office_id: [2] });
  });

  it('calls onFilterChange for date picker with various date scenarios', async () => {
    const onFilterChange = jest.fn();
    await act(async () => {
      render(
        <RAFilters filters={defaultFilters} onFilterChange={onFilterChange} />
      );
    });

    // Click date picker to trigger change (mocked to return [string, undefined])
    fireEvent.click(screen.getAllByTestId('mock-date-picker')[0]);
    expect(onFilterChange).toHaveBeenCalledWith('submitted_on', ['2023-01-01', null]);
  });

  it('renders date picker with existing dates', async () => {
    const filtersWithDates: RAFilterValues = {
      ...defaultFilters,
      submitted_on: ['2023-01-01', '2023-12-31'],
    };

    await act(async () => {
      render(
        <RAFilters filters={filtersWithDates} onFilterChange={jest.fn()} />
      );
    });

    // Check that dates are passed to the date picker
    const datePickers = screen.getAllByTestId('mock-date-picker');
    expect(datePickers.length).toBeGreaterThan(0);
  });

  it('calls onFilterChange for search dropdowns', async () => {
    const onFilterChange = jest.fn();
    await act(async () => {
      render(
        <RAFilters filters={defaultFilters} onFilterChange={onFilterChange} />
      );
    });

    // Clear previous calls from component initialization
    onFilterChange.mockClear();

    // Click first search dropdown (approval status) - should get first status value (APPROVED = 3)
    const searchDropdowns = screen.getAllByTestId('mock-search-dropdown');
    fireEvent.click(searchDropdowns[0]);
    expect(onFilterChange).toHaveBeenCalledWith('approval_status', [3]);
  });

  it('tests getRaBasicFiltersFormConfig with various filter states', () => {
    const onFilterChange = jest.fn();

    // Test with empty filters
    const emptyConfig = getRaBasicFiltersFormConfig(
      { filters: defaultFilters, onFilterChange },
      { vessels: [], vesselCategories: [], offices: [] }
    );
    expect(Array.isArray(emptyConfig)).toBe(true);
    expect(emptyConfig.length).toBe(6); // search, approval_status, vessel_id, vessel_category, ra_level, submission_date

    // Test with populated filters
    const populatedFilters: RAFilterValues = {
      search: 'test search',
      approval_status: [3], // APPROVED
      vessel_or_office: { vessel_id: [1], office_id: [2] },
      vessel_category: ['Category 1'],
      ra_level: [1], // ROUTINE
      submitted_on: ['2023-01-01', '2023-12-31'],
      approval_date: null,
      assessment_date: null,
    };

    const populatedConfig = getRaBasicFiltersFormConfig(
      { filters: populatedFilters, onFilterChange },
      {
        vessels: [{ label: 'Vessel 1', value: 1, status: 'active' }],
        vesselCategories: [{ label: 'Category 1', value: 'Category 1' }],
        offices: [{ label: 'Office 1', value: 1 }]
      }
    );

    expect(populatedConfig.length).toBe(6);
    populatedConfig.forEach(item => {
      expect(item).toHaveProperty('key');
      expect(item).toHaveProperty('component');
    });
  });

  it('handles async error in useEffect and shows toast', async () => {
    mockServices.getRAStringOptions.mockRejectedValueOnce(new Error('API Error'));
    mockServices.getVesselsList.mockRejectedValueOnce(new Error('API Error'));
    mockServices.getOfficesList.mockRejectedValueOnce(new Error('API Error'));

    await act(async () => {
      render(<RAFilters filters={defaultFilters} onFilterChange={jest.fn()} />);
    });

    await waitFor(() => {
      const { toast } = require('react-toastify');
      expect(toast.error).toHaveBeenCalledWith('Failed to load data. Please try again later.');
    });
  });

  it('successfully loads and sets vessel, office, and category data', async () => {
    await act(async () => {
      render(<RAFilters filters={defaultFilters} onFilterChange={jest.fn()} />);
    });

    // Wait for useEffect to complete
    await waitFor(() => {
      expect(mockServices.getRAStringOptions).toHaveBeenCalledWith('vessel_category');
      expect(mockServices.getVesselsList).toHaveBeenCalled();
      expect(mockServices.getOfficesList).toHaveBeenCalled();
    });

    // Check that vessel/office options are passed to the dropdown
    const vesselOfficeDropdown = screen.getByTestId('mock-vessel-office-dropdown');
    expect(vesselOfficeDropdown).toBeInTheDocument();

    // Check that more filters drawer receives the options data
    const moreFiltersDrawer = screen.getByTestId('mock-more-filters-drawer');
    expect(moreFiltersDrawer).toBeInTheDocument();
  });

  it('tests getVesselOfficeOptions function indirectly through component', async () => {
    const vessels: FilterOption[] = [
      { label: 'Active Vessel', value: 1, status: 'active' },
      { label: 'Pending Vessel', value: 2, status: 'pending_handover' },
      { label: 'Handed Over Vessel', value: 3, status: 'handed_over' }
    ];

    const offices: FilterOption[] = [
      { label: 'Office 1', value: 1 },
      { label: 'Office 2', value: 2 }
    ];

    mockServices.getVesselsList.mockResolvedValueOnce(
      vessels.map(v => ({ name: v.label, vessel: { id: v.value }, status: v.status }))
    );
    mockServices.getOfficesList.mockResolvedValueOnce(
      offices.map(o => ({ value: o.label, id: o.value }))
    );

    await act(async () => {
      render(<RAFilters filters={defaultFilters} onFilterChange={jest.fn()} />);
    });

    await waitFor(() => {
      const vesselOfficeOptions = screen.getByTestId('vessel-office-options');
      expect(vesselOfficeOptions).toBeInTheDocument();
      // The options should be structured with vessel groups by status and offices
      const optionsText = vesselOfficeOptions.textContent;
      expect(optionsText).toContain('Active Vessels'); // From vesselStatusAndLabelName mapping
    });
  });

  it('handles dropdown filter changes with empty values', async () => {
    const onFilterChange = jest.fn();

    await act(async () => {
      render(<RAFilters filters={defaultFilters} onFilterChange={onFilterChange} />);
    });

    // Clear previous calls
    onFilterChange.mockClear();

    // This tests the onChange: value => onFilterChange('approval_status', value ? [value] : []) logic
    const emptyButtons = screen.getAllByTestId('empty-value-btn');

    // Test approval status empty value
    fireEvent.click(emptyButtons[0]);
    expect(onFilterChange).toHaveBeenCalledWith('approval_status', []);

    // Test vessel category empty value (covers line 315)
    fireEvent.click(emptyButtons[1]);
    expect(onFilterChange).toHaveBeenCalledWith('vessel_category', []);

    // Test ra level empty value (covers line 325)
    fireEvent.click(emptyButtons[2]);
    expect(onFilterChange).toHaveBeenCalledWith('ra_level', []);
  });

  it('handles dropdown filter changes with non-empty values', async () => {
    const onFilterChange = jest.fn();

    await act(async () => {
      render(<RAFilters filters={defaultFilters} onFilterChange={onFilterChange} />);
    });

    // Clear previous calls
    onFilterChange.mockClear();

    // Test all dropdowns with values
    const searchDropdowns = screen.getAllByTestId('mock-search-dropdown');

    // Approval status dropdown (first dropdown)
    fireEvent.click(searchDropdowns[0]);
    expect(onFilterChange).toHaveBeenCalledWith('approval_status', [3]); // APPROVED

    // Vessel category dropdown (second dropdown)
    fireEvent.click(searchDropdowns[1]);
    expect(onFilterChange).toHaveBeenCalledWith('vessel_category', ['Category 1']);

    // RA level dropdown (third dropdown)
    fireEvent.click(searchDropdowns[2]);
    expect(onFilterChange).toHaveBeenCalledWith('ra_level', [3]); // CRITICAL
  });

  it('tests date range filter with null values', async () => {
    const filtersWithNullDates: RAFilterValues = {
      ...defaultFilters,
      submitted_on: [null, null],
    };

    await act(async () => {
      render(<RAFilters filters={filtersWithNullDates} onFilterChange={jest.fn()} />);
    });

    // Check that null dates are handled properly
    const datePickers = screen.getAllByTestId('mock-date-picker');
    expect(datePickers.length).toBeGreaterThan(0);

    // Check start and end date displays
    const startDateSpan = screen.getAllByTestId('start-date')[0];
    const endDateSpan = screen.getAllByTestId('end-date')[0];
    expect(startDateSpan.textContent).toBe('No start date');
    expect(endDateSpan.textContent).toBe('No end date');
  });

  it('exports raFiltersInitialState correctly', () => {
    expect(raFiltersInitialState).toEqual({
      search: null,
      approval_status: [],
      vessel_or_office: null,
      vessel_category: [],
      ra_level: [],
      submitted_on: null,
      approval_date: null,
      assessment_date: null,
    });
  });

  it('tests useMemo dependency changes', async () => {
    const onFilterChange = jest.fn();
    const { rerender } = render(<RAFilters filters={defaultFilters} onFilterChange={onFilterChange} />);

    // Wait for initial load
    await waitFor(() => {
      expect(mockServices.getVesselsList).toHaveBeenCalled();
    });

    // Change filters to trigger useMemo recalculation
    const newFilters: RAFilterValues = {
      ...defaultFilters,
      search: 'new search term',
    };

    await act(async () => {
      rerender(<RAFilters filters={newFilters} onFilterChange={onFilterChange} />);
    });

    // Component should re-render with new filters
    const searchInput = screen.getByTestId('mock-search-input');
    expect(searchInput).toHaveValue('new search term');
  });
});

import React from 'react';
import {Form} from 'react-bootstrap';
import DatePicker, {DatePickerProps} from 'react-datepicker';
import {CalendarIcon} from '../utils/svgIcons';
import {X} from 'react-bootstrap-icons';

type CustomDatePickerProps = {
  label: string;
  value?: Date;
  onChange: (date?: Date) => void;
  placeholder?: string;
  controlId: string;
  isRequired?: boolean;
  id?: string;
  errorMsg?: string;
  isInvalid?: boolean;
  onBlur?: () => void;
} & Pick<DatePickerProps, 'minDate' | 'maxDate'>;

const CustomDatePicker: React.FC<CustomDatePickerProps> = ({
  label,
  value,
  onChange,
  placeholder = 'Select Date',
  controlId,
  isRequired = false,
  id = 'custom-datepicker',
  errorMsg = '',
  isInvalid: isInvalidProp,
  onBlur,
  ...restProps
}) => {
  const isInvalid = isInvalidProp !== undefined ? (isInvalidProp && !!errorMsg) : (isRequired && !value && !!errorMsg);

  return (
    <Form.Group className="mb-3" controlId={controlId}>
      <Form.Label id={id + label}>{label}</Form.Label>
      <div className="position-relative nbp-datepicker">
        <DatePicker
          className={`form-control fs-14 pe-4 ${isInvalid ? 'is-invalid' : ''}`}
          wrapperClassName="w-100"
          selected={value}
          onChange={(date: Date | null) => onChange(date || undefined)}
          placeholderText={placeholder}
          dateFormat="dd MMM yyyy"
          isClearable
          minDate={new Date()}
          onKeyDown={e => e.preventDefault()}
          onBlur={onBlur}
          {...restProps}
        />
        {value && (
          <div className="position-absolute clear-icon">
            <button
              style={{
                all: 'unset', // Removes all default styles
              }}
              className="cursor-pointer"
              onClick={() => onChange(undefined)}
              data-testid="clear-date-button"
              aria-label="Clear date"
            >
              <X size={18} />
            </button>
          </div>
        )}
        {!isInvalid && !value && (
          <div className="position-absolute calendar-icon">
            <CalendarIcon />
          </div>
        )}
        {isInvalid && (
          <div
            className="invalid-feedback"
            style={{display: 'block'}}
            data-testid="error-message"
          >
            {errorMsg.length
              ? errorMsg
              : `${label.replace('*', '')} is required.`}
          </div>
        )}
      </div>
    </Form.Group>
  );
};

export default CustomDatePicker;

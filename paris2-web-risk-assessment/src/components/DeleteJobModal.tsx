import React, {useEffect, useState} from 'react';
import {<PERSON><PERSON>, Button} from 'react-bootstrap';
import {TemplateForm} from '../types/template';
import {RiskForm} from '../types';

type Props = {
  onClose: () => void;
  jobId: string;
  form: TemplateForm | RiskForm;
  setForm: (f: any) => void;
  type?: 'template' | 'risk';
};

export const DeleteJobModal: React.FC<Props> = ({
  onClose,
  jobId,
  form,
  setForm,
  type = 'template',
}) => {
  const [jobIndex, setJobIndex] = useState(0);

  // Helper function to check if form is RiskForm
  const isRiskForm = (form: TemplateForm | RiskForm): form is RiskForm => {
    return type === 'risk' || 'risk_job' in form;
  };

  // Helper function to get jobs array based on form type
  const getJobsArray = () => {
    if (isRiskForm(form)) {
      return form.risk_job || [];
    }
    return form.template_job || [];
  };

  useEffect(() => {
    const jobs = getJobsArray();
    if (jobs.length > 0) {
      if (isRiskForm(form)) {
        // For risk forms, jobId is actually the array index as string
        const index = parseInt(jobId, 10);
        if (!isNaN(index) && index >= 0 && index < jobs.length) {
          setJobIndex(index);
        }
      } else {
        // For template forms, find by job_id
        const templateJobs = jobs as any[];
        const index = templateJobs.findIndex(job => job.job_id === jobId);
        if (index !== -1) {
          setJobIndex(index);
        }
      }
    }
  }, [jobId, form, type]);

  const handleConfirm = () => {
    if (isRiskForm(form)) {
      // Handle risk form deletion
      const riskJobs = form.risk_job || [];
      if (riskJobs.length > 0) {
        const index = parseInt(jobId, 10);
        if (!isNaN(index) && index >= 0 && index < riskJobs.length) {
          const updatedJobs = riskJobs.filter((_, i) => i !== index);
          setForm((prev: RiskForm) => ({
            ...prev,
            risk_job: updatedJobs,
          }));
        }
      }
    } else {
      // Handle template form deletion
      const templateJobs = form.template_job || [];
      if (jobId && templateJobs.length > 0) {
        const updatedJobs = templateJobs.filter(job => job.job_id !== jobId);
        setForm((prev: TemplateForm) => ({
          ...prev,
          template_job: updatedJobs,
        }));
      }
    }
    onClose();
  };

  return (
    <Modal
      show
      onHide={onClose}
      size="lg"
      backdrop="static"
      dialogClassName="top-modal"
    >
      <Modal.Header>
        <Modal.Title>Deleting a Job Step</Modal.Title>
      </Modal.Header>
      <Modal.Body className="complete-project-modal">
        <div
          className="alert d-flex align-items-center fs-14 ra-alert-warning"
          role="alert"
          data-testid="error-alert"
        >
          <div>
            <strong>Are you sure you want to delete this job step?</strong>
            <span> This action is permamnent and cannot be undone.</span>
          </div>
        </div>
        <div className="delete-job-border">
          <div className="secondary-color fs-14 fw-500">JOB {jobIndex + 1}</div>
          <div className="fs-16 fw-600">
            {(() => {
              const jobs = getJobsArray();
              const job = jobs[jobIndex];
              if (job) {
                return `${job.job_step} - ${job.job_hazard}`;
              }
              return '';
            })()}
          </div>
        </div>
      </Modal.Body>

      <Modal.Footer>
        <Button
          variant="primary"
          className="me-2 fs-14"
          onClick={handleConfirm}
        >
          Delete Job Step
        </Button>
        <Button
          variant="secondary"
          className="me-2 fs-14"
          onClick={() => onClose()}
        >
          Cancel
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

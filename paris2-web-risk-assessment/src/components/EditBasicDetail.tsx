import React, {useState, useEffect} from 'react';
import {Col, Form, Row} from 'react-bootstrap';
import {TemplateForm} from '../types/template';
import {RiskForm} from '../types/risk';
import DropdownTypeahead from './DropdownTypeahead';
import SingleVesselOfficeDropdown from './SingleVesselOfficeDropdown';
import CustomDatePicker from './CustomDatePicker';
import {assessorOptions} from '../pages/CreateRA/BasicDetails';
import {
  formatDateToYYYYMMDD,
  createGroupedVesselOfficeOptions,
  findSelectedVesselOfficeOption,
  SingleVesselOfficeOption,
  GroupedVesselOfficeOption,
} from '../utils/helper';
import {useDataStoreContext} from '../context';
import {vesselStatusAndLabelName} from '../utils/common';

const EditBasicDetailsComp = ({
  clonedForm,
  setClonedForm,
  type = 'template',
}: {
  clonedForm: TemplateForm | RiskForm;
  setClonedForm: any;
  type?: 'template' | 'risk';
}) => {
  const {
    dataStore: {
      vesselListForRisk,
      officeListForRisk,
      approversReqListForRiskOffice,
      approversReqListForRiskVessel,
    },
  } = useDataStoreContext();
  const errorMsg = 'This is a mandatory field. Please fill to process.';
  const [approvalOptionsOffice, setApprovalOptionsOffice] = useState<
    {value: number; label: string}[]
  >([]);
  const [approvalOptionsVessel, setApprovalOptionsVessel] = useState<
    {value: number; label: string}[]
  >([]);
  const [groupedVesselOfficeOptions, setGroupedVesselOfficeOptions] = useState<
    GroupedVesselOfficeOption[]
  >([]);

  // Load vessel and office options for risk forms
  useEffect(() => {
    if (type === 'risk') {
      const loadOptions = async () => {
        try {
          // Create grouped options using helper function
          const groupedOptions = createGroupedVesselOfficeOptions(
            vesselListForRisk,
            officeListForRisk,
            vesselStatusAndLabelName,
          );
          setGroupedVesselOfficeOptions(groupedOptions);

          setApprovalOptionsOffice(
            approversReqListForRiskOffice.map(item => ({
              value: item.id,
              label: item.name,
            })),
          );

          setApprovalOptionsVessel(
            approversReqListForRiskVessel.map(item => ({
              value: item.id,
              label: item.name,
            })),
          );
        } catch (error) {
          console.error('Error loading vessel/office options:', error);
        }
      };

      loadOptions();
    }
  }, [
    type,
    vesselListForRisk,
    officeListForRisk,
    approversReqListForRiskOffice,
    approversReqListForRiskVessel,
  ]);

  // Handle dropdown changes for risk form
  const handleDropdownChange = (name: string, value: any) => {
    setClonedForm({
      ...clonedForm,
      [name]: value,
    });
  };

  // Handle vessel/office dropdown change
  const handleVesselOfficeChange = (
    selected: SingleVesselOfficeOption | null,
  ) => {
    const value = selected?.value ? Number(selected.value) : 0;
    const vesselId = selected?.vesselId;

    // Update both vessel_ownership_id and vessel_id for risk forms
    setClonedForm((prev: TemplateForm | RiskForm) => ({
      ...prev,
      vessel_ownership_id: value,
      ...(vesselId ? {vessel_id: vesselId} : {}),
    }));
  };

  // Handle approval required dropdown change
  const handleApprovalChange = (selected: any) => {
    let values: string[] = [];

    if (Array.isArray(selected)) {
      values = selected.map((item: any) => item.value);
    } else if (selected) {
      values = [selected.value];
    }

    setClonedForm({
      ...clonedForm,
      approval_required: values,
    });
  };

  // Handle date changes for risk form
  const handleDateChange = (name: string, date?: Date) => {
    setClonedForm({
      ...clonedForm,
      [name]: date ? formatDateToYYYYMMDD(date) : '',
    });
  };

  return (
    <>
      <Row className="mb-3">
        <Col>
          <Form.Group controlId="taskRequiringRa">
            <Form.Label className="fs-14 fw-500">
              Task Requiring R.A.
            </Form.Label>
            <Form.Control
              type="text"
              value={clonedForm.task_requiring_ra}
              onChange={e => {
                setClonedForm({
                  ...clonedForm,
                  task_requiring_ra: e.target.value,
                });
              }}
              maxLength={255}
              className="fs-14"
              isInvalid={
                !clonedForm.task_requiring_ra ||
                clonedForm.task_requiring_ra.trim() === ''
              }
            />
            <Form.Control.Feedback type="invalid">
              {errorMsg}
            </Form.Control.Feedback>
          </Form.Group>
        </Col>
      </Row>

      {/* Risk Form specific fields */}
      {type === 'risk' && (
        <>
          <Row className="mb-3">
            <Col md={6}>
              <DropdownTypeahead
                label="Assessor"
                options={assessorOptions}
                selected={assessorOptions.filter(
                  opt => opt.value === (clonedForm as RiskForm).assessor,
                )}
                onChange={(selected: any) => {
                  const value = Array.isArray(selected)
                    ? selected[0]?.value
                    : selected?.value;
                  handleDropdownChange('assessor', value);
                }}
                isInvalid={!(clonedForm as RiskForm).assessor}
                errorMessage={errorMsg}
              />
            </Col>
            <Col md={6}>
              <SingleVesselOfficeDropdown
                label="Vessel/Office"
                options={groupedVesselOfficeOptions}
                value={findSelectedVesselOfficeOption(
                  groupedVesselOfficeOptions,
                  (clonedForm as RiskForm).vessel_ownership_id,
                )}
                onChange={handleVesselOfficeChange}
                isInvalid={!(clonedForm as RiskForm).vessel_ownership_id}
                errorMessage={errorMsg}
                placeholder="Select Vessel/Office"
              />
            </Col>
          </Row>
          <Row className="mb-3">
            <Col md={6}>
              <CustomDatePicker
                label="Date of Risk Assessment"
                value={
                  (clonedForm as RiskForm).date_risk_assessment
                    ? new Date((clonedForm as RiskForm).date_risk_assessment)
                    : undefined
                }
                onChange={date =>
                  handleDateChange('date_risk_assessment', date)
                }
                placeholder="Select Date"
                controlId="date_risk_assessment"
                isRequired={true}
                errorMsg={
                  !(clonedForm as RiskForm).date_risk_assessment ? errorMsg : ''
                }
                minDate={undefined}
              />
            </Col>
            <Col md={6}>
              <DropdownTypeahead
                label="Approvals Required (if necessary)"
                options={
                  (clonedForm as RiskForm).assessor === 2
                    ? approvalOptionsVessel
                    : approvalOptionsOffice
                }
                selected={
                  (clonedForm as RiskForm).assessor === 2
                    ? approvalOptionsVessel.filter(opt =>
                        (clonedForm as RiskForm).approval_required?.includes(
                          opt.value,
                        ),
                      )
                    : approvalOptionsOffice.filter(opt =>
                        (clonedForm as RiskForm).approval_required?.includes(
                          opt.value,
                        ),
                      )
                }
                onChange={handleApprovalChange}
                multiple={true}
                isInvalid={
                  !(clonedForm as RiskForm).approval_required ||
                  (clonedForm as RiskForm).approval_required.length === 0
                }
                errorMessage={errorMsg}
                useCheckboxes
              />
            </Col>
          </Row>
        </>
      )}

      <Row>
        <Col>
          <Form.Group controlId="taskDuration">
            <Form.Label className="fs-14 fw-500">Task Duration</Form.Label>
            <Form.Control
              type="text"
              value={clonedForm.task_duration}
              onChange={e => {
                setClonedForm({
                  ...clonedForm,
                  task_duration: e.target.value,
                });
              }}
              maxLength={255}
              placeholder="Enter No. of Days Required"
              isInvalid={
                !clonedForm.task_duration ||
                clonedForm.task_duration.trim() === ''
              }
            />
            <Form.Control.Feedback type="invalid">
              {errorMsg}
            </Form.Control.Feedback>
          </Form.Group>
          <Form.Text className="text-muted fs-14">
            Mention if values are in Days/Hours
          </Form.Text>
        </Col>
      </Row>
    </>
  );
};

export default EditBasicDetailsComp;

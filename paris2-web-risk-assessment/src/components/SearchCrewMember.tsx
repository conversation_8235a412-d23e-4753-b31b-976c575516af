import React, {useState, useMemo, useRef, useEffect} from 'react';
import SearchInput from './SearchInput';
import '../styles/components/search-user-dropdown.scss';
import {getInitials} from './SearchUserDropdown';

interface IUser {
  id: string;
  full_name: string;
  subText: string;
}

interface UserSelectorDropdownProps {
  options: IUser[];
  onChange: (selectedUserId: string[]) => void;
  onClose: () => void;
}

const UserSelectorDropdown: React.FC<UserSelectorDropdownProps> = ({
  options = [],
  onChange,
  onClose,
}) => {
  const dropdownRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [onClose]);

  const handleUserClick = (userId: string) => {
    onChange([userId]);
  };

  return (
    <div
      ref={dropdownRef}
      className="user-selector-container user-selector-dropdown"
    >
      <div className="user-list-section overflowx-hidden">
        {options.length > 0 ? (
          options.map(user => (
            <button
              key={user.id}
              className="user-list-item reset-button"
              onClick={() => handleUserClick(user.id)}
              style={{zIndex: 10}}
            >
              <div className="avatar">{getInitials(user.full_name)}</div>
              <div className="user-info">
                <div className="user-name">{user.full_name}</div>
                <div className="user-details">{user.subText}</div>
              </div>
            </button>
          ))
        ) : (
          <div className="empty-list-message">No users found.</div>
        )}
      </div>
    </div>
  );
};

interface SearchUserDropdownProps {
  value: string[];
  options: IUser[];
  placeholder?: string;
  onChange: (selected: string[]) => void;
}

const SearchCrewMember: React.FC<SearchUserDropdownProps> = ({
  value,
  options,
  placeholder = 'Select user',
  onChange,
}) => {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [search, setSearch] = useState('');
  const inputRef = useRef<HTMLDivElement>(null);

  const filteredUsers = useMemo(() => {
    if (!search) return options;
    return options.filter(
      user =>
        user.full_name.toLowerCase().includes(search.toLowerCase()) ||
        user.subText.toLowerCase().includes(search.toLowerCase()),
    );
  }, [options, search]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setDropdownOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className="user-multiselect-root typeahead-wrapper" ref={inputRef}>
      <div
        className="user-search-input-wrapper"
        onFocus={() => setDropdownOpen(true)}
      >
        <SearchInput
          placeholder={placeholder}
          value={search}
          onSearch={val => {
            setSearch(val || '');
            setDropdownOpen(true);
          }}
          showClear
        />
      </div>

      {dropdownOpen && (
        <UserSelectorDropdown
          options={filteredUsers}
          onChange={selected => {
            onChange(selected);
            setDropdownOpen(false);
            setSearch('');
          }}
          onClose={() => setDropdownOpen(false)}
        />
      )}
    </div>
  );
};

export default SearchCrewMember;

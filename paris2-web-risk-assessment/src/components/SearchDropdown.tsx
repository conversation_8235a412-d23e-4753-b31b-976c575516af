import React, {
  useState,
  useMemo,
  useRef,
  useEffect,
  useLayoutEffect,
} from 'react';
import {ChevronDown} from 'react-bootstrap-icons';
import SearchInput from './SearchInput';
import CheckboxComponent from './CheckboxComponent';
import SingleBadgePopover from './SingleBadgePopover';
import {FilterOption} from '../pages/RAListing/components/RAFilters';
import CloseIcon from './icons/close-icon';

import '../styles/components/search-option-dropdown.scss';

interface DropdownItemsProps {
  options: FilterOption[];
  value: FilterOption['value'][];
  onChange: (selectedOptions: FilterOption['value'][]) => void;
  onClose: () => void;
  multiple?: boolean;
}

const DropdownItems: React.FC<DropdownItemsProps> = ({
  options = [],
  value,
  onChange,
  onClose,
  multiple = true,
}) => {
  const [search, setSearch] = useState<string>('');

  const filteredOptions = useMemo<FilterOption[]>(() => {
    if (!search) {
      return options;
    }
    return options.filter(item =>
      item.label.toLowerCase().includes(search.toLowerCase()),
    );
  }, [options, search]);

  const handleOptionSelection = (optionValue: FilterOption['value']): void => {
    if (multiple) {
      const numericValue = optionValue;
      if (value.includes(numericValue)) {
        onChange(value.filter(item => item !== numericValue));
      } else {
        onChange([...value, numericValue]);
      }
    } else {
      onChange([optionValue]);
      onClose();
    }
  };

  const handleSelectAll = (): void => {
    if (filteredOptions.length === 0) return;
    const allFilteredOptionValues = filteredOptions.map(item => item.value);
    const allCurrentlySelected = filteredOptions.every(item =>
      value.includes(item.value),
    );
    if (allCurrentlySelected) {
      onChange(value.filter(val => !allFilteredOptionValues.includes(val)));
    } else {
      onChange(Array.from(new Set([...value, ...allFilteredOptionValues])));
    }
  };

  // Close dropdown on outside click
  const dropdownRef = useRef<HTMLDivElement>(null);
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        onClose();
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [onClose]);

  return (
    <div
      ref={dropdownRef}
      className="option-selector-container option-selector-dropdown"
    >
      <div className="search-bar-section">
        <SearchInput
          placeholder="Search"
          value={search}
          onSearch={e => setSearch(e || '')}
        />
      </div>

      <div className="option-list-section">
        {filteredOptions.length > 0 ? (
          filteredOptions.map(item => (
            <div
              key={item.value}
              role="option"
              aria-selected={value.includes(item.value)}
              tabIndex={0}
              className={`option-list-item${!multiple ? ' single-select' : ''}${
                !multiple && value.includes(item.value) ? ' selected' : ''
              }`}
              onClick={() => handleOptionSelection(item.value)}
              onKeyDown={e => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault(); // prevent scroll on spacebar
                  handleOptionSelection(item.value);
                }
              }}
              style={{zIndex: 10}}
            >
              {multiple && (
                <CheckboxComponent
                  key={item.value}
                  id={`option-check-${item.value}`}
                  checked={value.includes(item.value)}
                  onChange={() => handleOptionSelection(item.value)}
                  className="option-checkbox"
                />
              )}
              <div className="option-name">{item.label}</div>
            </div>
          ))
        ) : (
          <div className="empty-list-message">No options found.</div>
        )}
      </div>

      {multiple && (
        <div className="footer-section">
          <button
            type="button"
            className="select-all-link"
            onClick={handleSelectAll}
          >
            {value.length === filteredOptions.length
              ? 'Clear all'
              : 'Select all'}
          </button>
        </div>
      )}
    </div>
  );
};

interface SearchDropdownProps {
  selected: FilterOption['value'][] | null;
  options: FilterOption[];
  placeholder?: string;
  onChange: (selected: FilterOption['value'][]) => void;
  maxDisplayNames?: number; // how many selected names to show before counter
  multiple?: boolean;
}

const SearchDropdown: React.FC<SearchDropdownProps> = ({
  selected,
  options,
  placeholder = 'Select option',
  onChange,
  maxDisplayNames: _maxDisplayNames = 2,
  multiple = true,
}) => {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [maxDisplayNames, setMaxDisplayNames] = useState(2);

  // Normalize selected to always be an array for consistent handling
  const normalizedSelected: FilterOption['value'][] = Array.isArray(selected)
    ? selected
    : [];

  const selectedOptions = options.filter(option =>
    normalizedSelected.includes(option.value),
  );
  const inputRef = useRef<HTMLDivElement>(null);
  const namesMeasureRef = useRef<HTMLSpanElement>(null);
  const counterMeasureRef = useRef<HTMLSpanElement>(null);

  useLayoutEffect(() => {
    if (!inputRef.current || !namesMeasureRef.current) return;
    const inputWidth = inputRef.current.offsetWidth;
    let availableWidth = inputWidth - 40; // leave space for chevron, padding, counter
    if (counterMeasureRef.current && selectedOptions.length > 2) {
      availableWidth -= counterMeasureRef.current.offsetWidth + 8;
    }
    let total = 0;
    let fit = 0;
    const children = Array.from(namesMeasureRef.current.children);
    for (const child of children) {
      const w = (child as HTMLElement).offsetWidth;
      if (total + w > availableWidth) break;
      total += w;
      fit++;
    }
    setMaxDisplayNames(Math.max(1, fit));
  }, [selectedOptions, dropdownOpen]);

  useEffect(() => {
    const handleResize = () => {
      setTimeout(() => {
        if (inputRef.current) {
          setMaxDisplayNames(2); // force re-measure
        }
      }, 100);
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const displayFirstNames = selectedOptions
    .slice(0, maxDisplayNames)
    .map(u => u.label)
    .join(', ');
  const extraCount = selectedOptions.length - maxDisplayNames;
  const extraNames = selectedOptions.slice(maxDisplayNames).map(u => u.label);
  const showCounterOnly = selectedOptions.length > maxDisplayNames;

  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (e.key === 'Enter' || e.key === ' ') {
      setDropdownOpen(open => !open);
      e.preventDefault();
    }
    if (e.key === 'Escape') {
      setDropdownOpen(false);
    }
  };

  return (
    <div className="option-multiselect-root typeahead-wrapper">
      <div
        className={`option-multiselect-input${
          dropdownOpen ? ' open' : ''
        } typeahead-multiselect form-control`}
        tabIndex={0}
        ref={inputRef}
        onClick={() => setDropdownOpen(open => !open)}
        onKeyDown={handleInputKeyDown}
        aria-haspopup="listbox"
        aria-expanded={dropdownOpen}
        aria-controls="search-dropdown-listbox"
        role="combobox"
      >
        <span
          className="option-multiselect-names"
          title={selectedOptions.map(u => u.label).join(', ')}
        >
          <span className="option-multiselect-names-list">
            {selectedOptions.length === 0 ? (
              <span className="option-multiselect-placeholder">
                {placeholder}
              </span>
            ) : (
              displayFirstNames
            )}
          </span>
          {showCounterOnly && (
            <span
              className="option-multiselect-counter"
              ref={counterMeasureRef}
            >
              <SingleBadgePopover
                items={extraNames}
                label={`+${extraCount} More`}
              />
            </span>
          )}
        </span>
        <span className="option-multiselect-icons">
          {!multiple &&
            (normalizedSelected.length > 0 ? (
              <span
                className="option-multiselect-clear"
                role="button"
                tabIndex={0}
                onClick={e => {
                  e.stopPropagation();
                  onChange([]);
                }}
                onKeyDown={e => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault(); // prevent scrolling on spacebar
                    e.stopPropagation();
                    onChange([]);
                  }
                }}
              >
                <CloseIcon width={12} height={12} />
              </span>
            ) : (
              <span className="option-multiselect-chevron">
                <ChevronDown size={14} />
              </span>
            ))}
          {multiple && (
            <span className="option-multiselect-chevron">
              <ChevronDown size={14} />
            </span>
          )}
        </span>
        <span
          style={{
            position: 'absolute',
            visibility: 'hidden',
            pointerEvents: 'none',
            height: 0,
            whiteSpace: 'nowrap',
          }}
          ref={namesMeasureRef}
        >
          {selectedOptions.map(u => (
            <span key={u.value} style={{marginRight: 4}}>
              {u.label}
              {', '}
            </span>
          ))}
        </span>
      </div>
      {dropdownOpen && (
        <div id="search-dropdown-listbox">
          <DropdownItems
            options={options}
            value={normalizedSelected}
            onChange={onChange}
            onClose={() => setDropdownOpen(false)}
            multiple={multiple}
          />
        </div>
      )}
    </div>
  );
};

export default SearchDropdown;
